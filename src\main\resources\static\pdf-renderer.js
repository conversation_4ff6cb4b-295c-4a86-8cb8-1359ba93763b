/**
 * PDF渲染器 - 负责将PDF解析结果渲染为可编辑的HTML元素
 */
class PDFRenderer {
    constructor() {
        this.currentData = null;
        this.isEditMode = true;
        this.scale = 1.0;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // 文件选择
        document.getElementById('fileInput').addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.uploadFile(e.target.files[0]);
            }
        });

        // 控制按钮
        document.getElementById('editModeBtn').addEventListener('click', () => this.setEditMode(true));
        document.getElementById('viewModeBtn').addEventListener('click', () => this.setEditMode(false));
        document.getElementById('exportBtn').addEventListener('click', () => this.exportText());
        document.getElementById('resetBtn').addEventListener('click', () => this.reset());
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => uploadArea.classList.add('dragover'), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => uploadArea.classList.remove('dragover'), false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.uploadFile(files[0]);
            }
        });
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    async uploadFile(file) {
        if (!file.type.includes('pdf')) {
            this.showError('请选择PDF文件');
            return;
        }

        if (file.size > 10 * 1024 * 1024) {
            this.showError('文件大小不能超过10MB');
            return;
        }

        this.showProgress();

        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/api/pdf/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentData = result.data;
                this.renderPDF(result.data);
                this.showSuccess(`成功解析PDF文件: ${file.name}`);
            } else {
                this.showError(result.message || '解析失败');
            }
        } catch (error) {
            console.error('上传失败:', error);
            this.showError('网络错误，请重试');
        } finally {
            this.hideProgress();
        }
    }

    renderPDF(data) {
        const container = document.getElementById('pdfContainer');
        container.innerHTML = '';

        // 更新结果信息
        document.getElementById('resultInfo').textContent = 
            `总页数: ${data.totalPages} | 文本元素: ${data.textElements.length}个`;

        // 按页面分组文本元素
        const pageElements = {};
        data.textElements.forEach(element => {
            if (!pageElements[element.pageNumber]) {
                pageElements[element.pageNumber] = [];
            }
            pageElements[element.pageNumber].push(element);
        });

        // 渲染每一页
        for (let pageNum = 1; pageNum <= data.totalPages; pageNum++) {
            const pageDiv = this.createPage(pageNum, data.pageWidths[pageNum - 1], data.pageHeights[pageNum - 1]);
            container.appendChild(pageDiv);

            if (pageElements[pageNum]) {
                this.renderTextElements(pageDiv, pageElements[pageNum], data.pageHeights[pageNum - 1]);
            }
        }

        this.showResult();
    }

    createPage(pageNumber, width, height) {
        const pageDiv = document.createElement('div');
        pageDiv.className = 'pdf-page';
        pageDiv.id = `page-${pageNumber}`;
        
        // 设置页面尺寸（转换为像素，PDF点数 * 1.33）
        const pixelWidth = width * 1.33 * this.scale;
        const pixelHeight = height * 1.33 * this.scale;
        
        pageDiv.style.width = `${pixelWidth}px`;
        pageDiv.style.height = `${pixelHeight}px`;
        pageDiv.style.position = 'relative';

        // 添加页码标识
        const pageLabel = document.createElement('div');
        pageLabel.textContent = `第 ${pageNumber} 页`;
        pageLabel.style.position = 'absolute';
        pageLabel.style.top = '-25px';
        pageLabel.style.left = '0';
        pageLabel.style.fontSize = '12px';
        pageLabel.style.color = '#666';
        pageDiv.appendChild(pageLabel);

        return pageDiv;
    }

    renderTextElements(pageDiv, elements, pageHeight) {
        elements.forEach(element => {
            const textSpan = document.createElement('span');
            textSpan.className = 'text-element';
            textSpan.textContent = element.text;
            
            // 设置位置（PDF坐标系转换为HTML坐标系）
            const x = element.x * 1.33 * this.scale;
            const y = (pageHeight - element.y - element.height) * 1.33 * this.scale;
            
            textSpan.style.left = `${x}px`;
            textSpan.style.top = `${y}px`;
            textSpan.style.fontSize = `${element.fontSize * 1.33 * this.scale}px`;
            
            if (element.fontName) {
                textSpan.style.fontFamily = this.getFontFamily(element.fontName);
            }
            
            if (element.color) {
                textSpan.style.color = element.color;
            }
            
            if (element.bold) {
                textSpan.style.fontWeight = 'bold';
            }
            
            if (element.italic) {
                textSpan.style.fontStyle = 'italic';
            }

            // 设置可编辑属性
            if (this.isEditMode) {
                textSpan.contentEditable = true;
                textSpan.spellcheck = false;
            }

            // 添加事件监听
            textSpan.addEventListener('focus', () => {
                textSpan.style.zIndex = '1000';
            });

            textSpan.addEventListener('blur', () => {
                textSpan.style.zIndex = 'auto';
            });

            pageDiv.appendChild(textSpan);
        });
    }

    getFontFamily(pdfFontName) {
        // 将PDF字体名称映射到Web字体
        const fontMap = {
            'TimesRoman': 'Times, serif',
            'Times-Roman': 'Times, serif',
            'Times-Bold': 'Times, serif',
            'Times-Italic': 'Times, serif',
            'Times-BoldItalic': 'Times, serif',
            'Helvetica': 'Arial, sans-serif',
            'Helvetica-Bold': 'Arial, sans-serif',
            'Helvetica-Oblique': 'Arial, sans-serif',
            'Helvetica-BoldOblique': 'Arial, sans-serif',
            'Courier': 'Courier New, monospace',
            'Courier-Bold': 'Courier New, monospace',
            'Courier-Oblique': 'Courier New, monospace',
            'Courier-BoldOblique': 'Courier New, monospace'
        };

        return fontMap[pdfFontName] || 'Arial, sans-serif';
    }

    setEditMode(isEdit) {
        this.isEditMode = isEdit;
        
        // 更新按钮状态
        document.getElementById('editModeBtn').classList.toggle('active', isEdit);
        document.getElementById('viewModeBtn').classList.toggle('active', !isEdit);
        
        // 更新所有文本元素的可编辑状态
        document.querySelectorAll('.text-element').forEach(element => {
            element.contentEditable = isEdit;
            if (!isEdit) {
                element.blur();
            }
        });
    }

    exportText() {
        if (!this.currentData) return;

        let text = '';
        const pages = {};
        
        // 按页面收集文本
        document.querySelectorAll('.pdf-page').forEach(page => {
            const pageNum = parseInt(page.id.split('-')[1]);
            const elements = Array.from(page.querySelectorAll('.text-element'));
            
            // 按位置排序（从上到下，从左到右）
            elements.sort((a, b) => {
                const aTop = parseInt(a.style.top);
                const bTop = parseInt(b.style.top);
                if (Math.abs(aTop - bTop) < 10) { // 同一行
                    return parseInt(a.style.left) - parseInt(b.style.left);
                }
                return aTop - bTop;
            });
            
            pages[pageNum] = elements.map(el => el.textContent).join('');
        });

        // 组合所有页面的文本
        for (let i = 1; i <= this.currentData.totalPages; i++) {
            if (pages[i]) {
                text += `=== 第 ${i} 页 ===\n${pages[i]}\n\n`;
            }
        }

        // 下载文本文件
        const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.currentData.fileName || 'pdf-text'}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    reset() {
        this.currentData = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('pdfContainer').innerHTML = '';
        this.hideResult();
        this.hideProgress();
    }

    showProgress() {
        document.getElementById('progressSection').style.display = 'block';
        document.getElementById('progressFill').style.width = '100%';
    }

    hideProgress() {
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('progressFill').style.width = '0%';
    }

    showResult() {
        document.getElementById('resultSection').style.display = 'block';
    }

    hideResult() {
        document.getElementById('resultSection').style.display = 'none';
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // 移除现有消息
        document.querySelectorAll('.error-message, .success-message').forEach(el => el.remove());
        
        const messageDiv = document.createElement('div');
        messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        messageDiv.textContent = message;
        
        const uploadSection = document.querySelector('.upload-section');
        uploadSection.appendChild(messageDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
}

// 初始化PDF渲染器
document.addEventListener('DOMContentLoaded', () => {
    new PDFRenderer();
});
