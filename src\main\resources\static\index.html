<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF OCR 解析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .upload-section {
            padding: 40px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 60px 20px;
            margin: 20px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f2ff;
        }

        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .progress-section {
            padding: 20px 40px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #eee;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result-section {
            padding: 40px;
            display: none;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .result-title {
            font-size: 1.5em;
            color: #333;
        }

        .result-info {
            color: #666;
            font-size: 0.9em;
        }

        .pdf-canvas-container {
            position: relative;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            margin-bottom: 20px;
        }

        .pdf-page {
            position: relative;
            margin: 20px auto;
            border: 1px solid #ccc;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            background: white;
        }

        .text-element {
            position: absolute;
            border: 1px solid transparent;
            cursor: text;
            white-space: pre;
            line-height: 1;
            transition: all 0.2s ease;
        }

        .text-element:hover {
            border-color: #667eea;
            background-color: rgba(102, 126, 234, 0.1);
        }

        .text-element:focus {
            outline: 2px solid #667eea;
            background-color: rgba(102, 126, 234, 0.2);
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #f0f2ff;
            border-color: #667eea;
        }

        .control-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #c33;
        }

        .success-message {
            background: #efe;
            color: #363;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #363;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .upload-section, .result-section {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 PDF OCR 解析系统</h1>
            <p>上传PDF文件，智能解析并还原原始布局，支持在线编辑</p>
        </div>

        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    拖拽PDF文件到此处，或点击选择文件<br>
                    <small>支持PDF格式，最大10MB</small>
                </div>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".pdf" />
            </div>
        </div>

        <div class="progress-section" id="progressSection">
            <p>正在解析PDF文件...</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="result-section" id="resultSection">
            <div class="result-header">
                <div class="result-title">解析结果</div>
                <div class="result-info" id="resultInfo"></div>
            </div>
            
            <div class="controls">
                <button class="control-btn active" id="editModeBtn">编辑模式</button>
                <button class="control-btn" id="viewModeBtn">预览模式</button>
                <button class="control-btn" id="exportBtn">导出文本</button>
                <button class="control-btn" id="resetBtn">重新上传</button>
            </div>

            <div class="pdf-canvas-container" id="pdfContainer">
                <!-- PDF页面将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="pdf-renderer.js"></script>
</body>
</html>
