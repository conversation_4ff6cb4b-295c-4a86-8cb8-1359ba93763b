@echo off
chcp 65001 >nul
setlocal

REM Elasticsearch 修复脚本
set BASE_URL=http://localhost:8088/elasticsearch

echo === Elasticsearch 修复脚本 ===
echo 这个脚本将删除旧索引并重新创建
echo.

REM 1. 删除可能存在的错误索引
echo 1. 删除旧索引...
curl -X DELETE "%BASE_URL%/index/delete" -H "Content-Type: application/json"
echo.

REM 等待一下
timeout /t 2 /nobreak >nul

REM 2. 重新创建索引
echo 2. 重新创建索引...
curl -X POST "%BASE_URL%/index/create" -H "Content-Type: application/json"
echo.

REM 3. 初始化测试数据
echo 3. 初始化测试数据...
curl -X POST "%BASE_URL%/data/init" -H "Content-Type: application/json"
echo.

REM 4. 测试搜索功能
echo 4. 测试搜索功能...
curl -X GET "%BASE_URL%/product/search/all"
echo.

echo === 修复完成 ===
echo 如果还有问题，请检查Elasticsearch服务是否正常运行
pause
