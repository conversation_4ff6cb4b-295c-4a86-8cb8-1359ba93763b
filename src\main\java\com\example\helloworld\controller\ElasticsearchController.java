package com.example.helloworld.controller;

import com.example.helloworld.entity.Product;
import com.example.helloworld.service.ElasticsearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch演示控制器
 */
@Slf4j
@RestController
@RequestMapping("/elasticsearch")
public class ElasticsearchController {

    @Autowired
    private ElasticsearchService elasticsearchService;

    /**
     * 删除索引
     */
    @DeleteMapping("/index/delete")
    public Map<String, Object> deleteIndex() {
        Map<String, Object> result = new HashMap<>();
        boolean success = elasticsearchService.deleteIndex();
        result.put("success", success);
        result.put("message", success ? "索引删除成功" : "索引删除失败");
        return result;
    }

    /**
     * 创建索引
     */
    @PostMapping("/index/create")
    public Map<String, Object> createIndex() {
        Map<String, Object> result = new HashMap<>();
        boolean success = elasticsearchService.createIndex();
        result.put("success", success);
        result.put("message", success ? "索引创建成功" : "索引创建失败");
        return result;
    }

    /**
     * 初始化测试数据
     */
    @PostMapping("/data/init")
    public Map<String, Object> initTestData() {
        Map<String, Object> result = new HashMap<>();
        
        // 创建测试数据
        List<Product> products = Arrays.asList(
                new Product("1", "iPhone 15 Pro", "苹果最新款手机，搭载A17 Pro芯片", 
                        new BigDecimal("7999.00"), "手机", new String[]{"苹果", "智能手机", "5G"}, 
                        100, true, LocalDateTime.now(), LocalDateTime.now()),
                        
                new Product("2", "华为Mate 60 Pro", "华为旗舰手机，麒麟9000S芯片", 
                        new BigDecimal("6999.00"), "手机", new String[]{"华为", "智能手机", "5G"}, 
                        80, true, LocalDateTime.now(), LocalDateTime.now()),
                        
                new Product("3", "小米14 Ultra", "小米影像旗舰，徕卡光学镜头", 
                        new BigDecimal("5999.00"), "手机", new String[]{"小米", "智能手机", "摄影"}, 
                        120, true, LocalDateTime.now(), LocalDateTime.now()),
                        
                new Product("4", "MacBook Pro 16", "苹果笔记本电脑，M3 Max芯片", 
                        new BigDecimal("25999.00"), "电脑", new String[]{"苹果", "笔记本", "办公"}, 
                        50, true, LocalDateTime.now(), LocalDateTime.now()),
                        
                new Product("5", "ThinkPad X1 Carbon", "联想商务笔记本，轻薄便携", 
                        new BigDecimal("12999.00"), "电脑", new String[]{"联想", "笔记本", "商务"}, 
                        30, true, LocalDateTime.now(), LocalDateTime.now()),
                        
                new Product("6", "iPad Pro 12.9", "苹果平板电脑，M2芯片", 
                        new BigDecimal("8999.00"), "平板", new String[]{"苹果", "平板", "创作"}, 
                        60, true, LocalDateTime.now(), LocalDateTime.now())
        );

        boolean success = elasticsearchService.saveBatchDocuments(products);
        result.put("success", success);
        result.put("message", success ? "测试数据初始化成功" : "测试数据初始化失败");
        result.put("count", products.size());
        return result;
    }

    /**
     * 保存单个商品
     */
    @PostMapping("/product/save")
    public Map<String, Object> saveProduct(@RequestBody Product product) {
        Map<String, Object> result = new HashMap<>();
        
        if (product.getCreateTime() == null) {
            product.setCreateTime(LocalDateTime.now());
        }
        product.setUpdateTime(LocalDateTime.now());
        
        boolean success = elasticsearchService.saveDocument(product);
        result.put("success", success);
        result.put("message", success ? "商品保存成功" : "商品保存失败");
        result.put("product", product);
        return result;
    }

    /**
     * 根据ID获取商品
     */
    @GetMapping("/product/{id}")
    public Map<String, Object> getProduct(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();
        Product product = elasticsearchService.getDocumentById(id);
        
        result.put("success", product != null);
        result.put("message", product != null ? "获取商品成功" : "商品不存在");
        result.put("product", product);
        return result;
    }

    /**
     * 更新商品
     */
    @PutMapping("/product/{id}")
    public Map<String, Object> updateProduct(@PathVariable String id, @RequestBody Map<String, Object> updateFields) {
        Map<String, Object> result = new HashMap<>();
        
        // 添加更新时间
        updateFields.put("updateTime", LocalDateTime.now().toString());
        
        boolean success = elasticsearchService.updateDocument(id, updateFields);
        result.put("success", success);
        result.put("message", success ? "商品更新成功" : "商品更新失败");
        return result;
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/product/{id}")
    public Map<String, Object> deleteProduct(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();
        boolean success = elasticsearchService.deleteDocument(id);
        result.put("success", success);
        result.put("message", success ? "商品删除成功" : "商品删除失败");
        return result;
    }

    /**
     * 搜索所有商品
     */
    @GetMapping("/product/search/all")
    public Map<String, Object> searchAllProducts() {
        Map<String, Object> result = new HashMap<>();
        List<Product> products = elasticsearchService.searchAllDocuments();
        
        result.put("success", true);
        result.put("message", "搜索完成");
        result.put("count", products.size());
        result.put("products", products);
        return result;
    }

    /**
     * 根据名称搜索商品
     */
    @GetMapping("/product/search/name")
    public Map<String, Object> searchByName(@RequestParam String name) {
        Map<String, Object> result = new HashMap<>();
        List<Product> products = elasticsearchService.searchByName(name);
        
        result.put("success", true);
        result.put("message", "搜索完成");
        result.put("count", products.size());
        result.put("products", products);
        return result;
    }

    /**
     * 根据分类搜索商品
     */
    @GetMapping("/product/search/category")
    public Map<String, Object> searchByCategory(@RequestParam String category) {
        Map<String, Object> result = new HashMap<>();
        List<Product> products = elasticsearchService.searchByCategory(category);
        
        result.put("success", true);
        result.put("message", "搜索完成");
        result.put("count", products.size());
        result.put("products", products);
        return result;
    }

    /**
     * 复合搜索（名称和分类）
     */
    @GetMapping("/product/search/complex")
    public Map<String, Object> searchByNameAndCategory(@RequestParam String name, @RequestParam String category) {
        Map<String, Object> result = new HashMap<>();
        List<Product> products = elasticsearchService.searchByNameAndCategory(name, category);
        
        result.put("success", true);
        result.put("message", "搜索完成");
        result.put("count", products.size());
        result.put("products", products);
        return result;
    }
}
