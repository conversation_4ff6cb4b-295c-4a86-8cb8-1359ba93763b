@echo off
chcp 65001 >nul
setlocal

REM 简化的Elasticsearch测试脚本
set BASE_URL=http://localhost:8088/elasticsearch

echo === Elasticsearch 简化测试 ===
echo 确保应用已启动在端口 8088
echo.

REM 1. 删除旧索引（如果存在）
echo 1. 清理旧索引...
curl -s -X DELETE "%BASE_URL%/index/delete" -H "Content-Type: application/json" >nul 2>&1

REM 2. 创建索引
echo 2. 创建索引...
curl -X POST "%BASE_URL%/index/create" -H "Content-Type: application/json"
echo.

REM 3. 初始化测试数据
echo 3. 初始化测试数据...
curl -X POST "%BASE_URL%/data/init" -H "Content-Type: application/json"
echo.

REM 4. 搜索所有商品
echo 4. 搜索所有商品...
curl -X GET "%BASE_URL%/product/search/all"
echo.

REM 5. 根据名称搜索
echo 5. 搜索iPhone...
curl -X GET "%BASE_URL%/product/search/name?name=iPhone"
echo.

echo === 测试完成 ===
echo 如果看到商品数据，说明Elasticsearch集成成功！
pause
