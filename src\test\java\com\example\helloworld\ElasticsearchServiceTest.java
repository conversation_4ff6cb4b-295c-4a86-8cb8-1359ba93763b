package com.example.helloworld;

import com.example.helloworld.entity.Product;
import com.example.helloworld.service.ElasticsearchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch服务测试类
 */
@SpringBootTest(classes = com.example._09elastic.Application.class)
public class ElasticsearchServiceTest {

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Test
    public void testCreateIndex() {
        boolean result = elasticsearchService.createIndex();
        System.out.println("创建索引结果: " + result);
    }

    @Test
    public void testSaveDocument() {
        Product product = new Product(
                "test-1",
                "测试商品",
                "这是一个测试商品",
                new BigDecimal("99.99"),
                "测试分类",
                new String[]{"测试", "商品"},
                10,
                true,
                LocalDateTime.now(),
                LocalDateTime.now()
        );

        boolean result = elasticsearchService.saveDocument(product);
        System.out.println("保存文档结果: " + result);
    }

    @Test
    public void testGetDocument() {
        Product product = elasticsearchService.getDocumentById("test-1");
        System.out.println("获取文档结果: " + product);
    }

    @Test
    public void testUpdateDocument() {
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put("price", 88.88);
        updateFields.put("stock", 20);

        boolean result = elasticsearchService.updateDocument("test-1", updateFields);
        System.out.println("更新文档结果: " + result);
    }

    @Test
    public void testSearchByName() {
        List<Product> products = elasticsearchService.searchByName("测试");
        System.out.println("根据名称搜索结果: " + products.size() + " 条记录");
        products.forEach(System.out::println);
    }

    @Test
    public void testSearchAll() {
        List<Product> products = elasticsearchService.searchAllDocuments();
        System.out.println("搜索所有文档结果: " + products.size() + " 条记录");
        products.forEach(System.out::println);
    }

    @Test
    public void testDeleteDocument() {
        boolean result = elasticsearchService.deleteDocument("test-1");
        System.out.println("删除文档结果: " + result);
    }

    @Test
    public void testBatchSave() {
        List<Product> products = Arrays.asList(
                new Product("batch-1", "批量测试商品1", "批量测试描述1", 
                        new BigDecimal("100.00"), "测试", new String[]{"批量", "测试"}, 
                        10, true, LocalDateTime.now(), LocalDateTime.now()),
                new Product("batch-2", "批量测试商品2", "批量测试描述2", 
                        new BigDecimal("200.00"), "测试", new String[]{"批量", "测试"}, 
                        20, true, LocalDateTime.now(), LocalDateTime.now())
        );

        boolean result = elasticsearchService.saveBatchDocuments(products);
        System.out.println("批量保存结果: " + result);
    }
}
