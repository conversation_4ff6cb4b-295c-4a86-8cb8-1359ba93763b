package com.example.helloworld.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.CreateIndexRequest;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import com.example.helloworld.entity.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch服务类
 */
@Slf4j
@Service
public class ElasticsearchService {

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    private static final String INDEX_NAME = "products";

    /**
     * 创建索引
     */
    public boolean createIndex() {
        try {
            // 检查索引是否存在
            ExistsRequest existsRequest = ExistsRequest.of(e -> e.index(INDEX_NAME));
            boolean exists = elasticsearchClient.indices().exists(existsRequest).value();
            
            if (exists) {
                log.info("索引 {} 已存在", INDEX_NAME);
                return true;
            }

            // 创建索引
            CreateIndexRequest createIndexRequest = CreateIndexRequest.of(c -> c
                    .index(INDEX_NAME)
                    .mappings(m -> m
                            .properties("id", p -> p.keyword(k -> k))
                            .properties("name", p -> p.text(t -> t.analyzer("ik_max_word")))
                            .properties("description", p -> p.text(t -> t.analyzer("ik_max_word")))
                            .properties("price", p -> p.double_(d -> d))
                            .properties("category", p -> p.keyword(k -> k))
                            .properties("tags", p -> p.keyword(k -> k))
                            .properties("stock", p -> p.integer(i -> i))
                            .properties("available", p -> p.boolean_(b -> b))
                            .properties("createTime", p -> p.date(d -> d.format("yyyy-MM-dd HH:mm:ss")))
                            .properties("updateTime", p -> p.date(d -> d.format("yyyy-MM-dd HH:mm:ss")))
                    )
            );

            elasticsearchClient.indices().create(createIndexRequest);
            log.info("索引 {} 创建成功", INDEX_NAME);
            return true;
        } catch (IOException e) {
            log.error("创建索引失败", e);
            return false;
        }
    }

    /**
     * 保存文档
     */
    public boolean saveDocument(Product product) {
        try {
            IndexRequest<Product> request = IndexRequest.of(i -> i
                    .index(INDEX_NAME)
                    .id(product.getId())
                    .document(product)
            );

            IndexResponse response = elasticsearchClient.index(request);
            log.info("文档保存成功，ID: {}, Result: {}", response.id(), response.result());
            return true;
        } catch (IOException e) {
            log.error("保存文档失败", e);
            return false;
        }
    }

    /**
     * 批量保存文档
     */
    public boolean saveBatchDocuments(List<Product> products) {
        try {
            BulkRequest.Builder bulkBuilder = new BulkRequest.Builder();
            
            for (Product product : products) {
                bulkBuilder.operations(op -> op
                        .index(idx -> idx
                                .index(INDEX_NAME)
                                .id(product.getId())
                                .document(product)
                        )
                );
            }

            BulkResponse response = elasticsearchClient.bulk(bulkBuilder.build());
            
            if (response.errors()) {
                log.error("批量保存存在错误");
                response.items().forEach(item -> {
                    if (item.error() != null) {
                        log.error("错误项: {}", item.error().reason());
                    }
                });
                return false;
            }
            
            log.info("批量保存成功，共 {} 条记录", products.size());
            return true;
        } catch (IOException e) {
            log.error("批量保存文档失败", e);
            return false;
        }
    }

    /**
     * 根据ID获取文档
     */
    public Product getDocumentById(String id) {
        try {
            GetRequest request = GetRequest.of(g -> g
                    .index(INDEX_NAME)
                    .id(id)
            );

            GetResponse<Product> response = elasticsearchClient.get(request, Product.class);
            
            if (response.found()) {
                log.info("找到文档，ID: {}", id);
                return response.source();
            } else {
                log.info("未找到文档，ID: {}", id);
                return null;
            }
        } catch (IOException e) {
            log.error("获取文档失败", e);
            return null;
        }
    }

    /**
     * 更新文档
     */
    public boolean updateDocument(String id, Map<String, Object> updateFields) {
        try {
            UpdateRequest<Product, Map<String, Object>> request = UpdateRequest.of(u -> u
                    .index(INDEX_NAME)
                    .id(id)
                    .doc(updateFields)
            );

            UpdateResponse<Product> response = elasticsearchClient.update(request, Product.class);
            log.info("文档更新成功，ID: {}, Result: {}", response.id(), response.result());
            return true;
        } catch (IOException e) {
            log.error("更新文档失败", e);
            return false;
        }
    }

    /**
     * 删除文档
     */
    public boolean deleteDocument(String id) {
        try {
            DeleteRequest request = DeleteRequest.of(d -> d
                    .index(INDEX_NAME)
                    .id(id)
            );

            DeleteResponse response = elasticsearchClient.delete(request);
            log.info("文档删除成功，ID: {}, Result: {}", response.id(), response.result());
            return true;
        } catch (IOException e) {
            log.error("删除文档失败", e);
            return false;
        }
    }

    /**
     * 搜索所有文档
     */
    public List<Product> searchAllDocuments() {
        try {
            SearchRequest request = SearchRequest.of(s -> s
                    .index(INDEX_NAME)
                    .query(q -> q.matchAll(m -> m))
                    .size(100)
            );

            SearchResponse<Product> response = elasticsearchClient.search(request, Product.class);
            
            List<Product> products = new ArrayList<>();
            for (Hit<Product> hit : response.hits().hits()) {
                products.add(hit.source());
            }
            
            log.info("搜索到 {} 条记录", products.size());
            return products;
        } catch (IOException e) {
            log.error("搜索文档失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据名称搜索文档
     */
    public List<Product> searchByName(String name) {
        try {
            Query query = Query.of(q -> q
                    .match(m -> m
                            .field("name")
                            .query(name)
                    )
            );

            SearchRequest request = SearchRequest.of(s -> s
                    .index(INDEX_NAME)
                    .query(query)
                    .size(100)
            );

            SearchResponse<Product> response = elasticsearchClient.search(request, Product.class);
            
            List<Product> products = new ArrayList<>();
            for (Hit<Product> hit : response.hits().hits()) {
                products.add(hit.source());
            }
            
            log.info("根据名称 '{}' 搜索到 {} 条记录", name, products.size());
            return products;
        } catch (IOException e) {
            log.error("根据名称搜索文档失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据分类搜索文档
     */
    public List<Product> searchByCategory(String category) {
        try {
            Query query = Query.of(q -> q
                    .term(t -> t
                            .field("category")
                            .value(category)
                    )
            );

            SearchRequest request = SearchRequest.of(s -> s
                    .index(INDEX_NAME)
                    .query(query)
                    .size(100)
            );

            SearchResponse<Product> response = elasticsearchClient.search(request, Product.class);
            
            List<Product> products = new ArrayList<>();
            for (Hit<Product> hit : response.hits().hits()) {
                products.add(hit.source());
            }
            
            log.info("根据分类 '{}' 搜索到 {} 条记录", category, products.size());
            return products;
        } catch (IOException e) {
            log.error("根据分类搜索文档失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 复合搜索（名称和分类）
     */
    public List<Product> searchByNameAndCategory(String name, String category) {
        try {
            Query query = Query.of(q -> q
                    .bool(b -> b
                            .must(m -> m
                                    .match(mt -> mt
                                            .field("name")
                                            .query(name)
                                    )
                            )
                            .must(m -> m
                                    .term(t -> t
                                            .field("category")
                                            .value(category)
                                    )
                            )
                    )
            );

            SearchRequest request = SearchRequest.of(s -> s
                    .index(INDEX_NAME)
                    .query(query)
                    .size(100)
            );

            SearchResponse<Product> response = elasticsearchClient.search(request, Product.class);
            
            List<Product> products = new ArrayList<>();
            for (Hit<Product> hit : response.hits().hits()) {
                products.add(hit.source());
            }
            
            log.info("根据名称 '{}' 和分类 '{}' 搜索到 {} 条记录", name, category, products.size());
            return products;
        } catch (IOException e) {
            log.error("复合搜索文档失败", e);
            return new ArrayList<>();
        }
    }
}
