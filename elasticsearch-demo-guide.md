# Elasticsearch Demo 使用指南

## 项目概述

这是一个基于SpringBoot 2.7.9和Elasticsearch 8.11.0的演示项目，展示了如何在SpringBoot应用中集成和使用Elasticsearch进行数据存储和搜索。

## 版本兼容性

- **SpringBoot**: 2.7.9
- **Elasticsearch**: 8.11.0
- **Java**: 17
- **Spring Cloud**: 2021.0.5

## 项目结构

```
src/main/java/com/example/helloworld/
├── config/
│   └── ElasticsearchConfig.java          # Elasticsearch配置类
├── entity/
│   └── Product.java                       # 商品实体类
├── service/
│   └── ElasticsearchService.java         # Elasticsearch服务类
└── controller/
    └── ElasticsearchController.java      # REST API控制器
```

## 功能特性

### 1. 基础功能
- 创建索引
- 保存文档（单个/批量）
- 根据ID获取文档
- 更新文档
- 删除文档

### 2. 搜索功能
- 搜索所有文档
- 根据名称搜索（支持中文分词）
- 根据分类搜索
- 复合搜索（名称+分类）

## 启动前准备

### 1. 启动Elasticsearch

确保你的Elasticsearch 8.11.0服务正在运行：

```bash
# 默认地址：http://localhost:9200
# 检查服务状态
curl http://localhost:9200
```

### 2. 配置文件

项目已在 `bootstrap.yml` 中配置了Elasticsearch连接信息：

```yaml
elasticsearch:
  host: localhost
  port: 9200
  scheme: http
```

如需修改，请根据你的Elasticsearch实际配置调整。

## API接口说明

### 1. 索引管理

#### 创建索引
```http
POST http://localhost:8088/elasticsearch/index/create
```

#### 初始化测试数据
```http
POST http://localhost:8088/elasticsearch/data/init
```

### 2. 商品管理

#### 保存商品
```http
POST http://localhost:8088/elasticsearch/product/save
Content-Type: application/json

{
    "id": "7",
    "name": "华为MateBook X Pro",
    "description": "华为轻薄笔记本电脑，3K触控屏",
    "price": 8999.00,
    "category": "电脑",
    "tags": ["华为", "笔记本", "轻薄"],
    "stock": 40,
    "available": true
}
```

#### 获取商品
```http
GET http://localhost:8088/elasticsearch/product/1
```

#### 更新商品
```http
PUT http://localhost:8088/elasticsearch/product/1
Content-Type: application/json

{
    "price": 7599.00,
    "stock": 90
}
```

#### 删除商品
```http
DELETE http://localhost:8088/elasticsearch/product/1
```

### 3. 搜索功能

#### 搜索所有商品
```http
GET http://localhost:8088/elasticsearch/product/search/all
```

#### 根据名称搜索
```http
GET http://localhost:8088/elasticsearch/product/search/name?name=iPhone
```

#### 根据分类搜索
```http
GET http://localhost:8088/elasticsearch/product/search/category?category=手机
```

#### 复合搜索
```http
GET http://localhost:8088/elasticsearch/product/search/complex?name=华为&category=手机
```

## 使用步骤

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 创建索引
```bash
curl -X POST http://localhost:8088/elasticsearch/index/create
```

### 3. 初始化测试数据
```bash
curl -X POST http://localhost:8088/elasticsearch/data/init
```

### 4. 测试搜索功能
```bash
# 搜索所有商品
curl http://localhost:8088/elasticsearch/product/search/all

# 搜索iPhone相关商品
curl "http://localhost:8088/elasticsearch/product/search/name?name=iPhone"

# 搜索手机分类商品
curl "http://localhost:8088/elasticsearch/product/search/category?category=手机"
```

## 注意事项

### 1. 中文分词
项目已经修改为使用自定义的中文分析器，不再依赖IK分词插件。如果遇到分析器相关错误：

**解决方案**：
1. 运行修复脚本：`fix-elasticsearch.bat`
2. 或者手动执行：
   ```bash
   # 删除旧索引
   curl -X DELETE http://localhost:8088/elasticsearch/index/delete

   # 重新创建索引
   curl -X POST http://localhost:8088/elasticsearch/index/create
   ```

**如果你想使用IK分词插件**：
- 下载对应版本的IK插件
- 安装到Elasticsearch的plugins目录
- 重启Elasticsearch
- 修改代码中的分析器为 `ik_max_word`

### 2. 索引映射
项目为商品实体定义了详细的索引映射：
- `id`: keyword类型，精确匹配
- `name`, `description`: text类型，支持全文搜索
- `price`: double类型
- `category`, `tags`: keyword类型
- `stock`: integer类型
- `available`: boolean类型
- `createTime`, `updateTime`: date类型

### 3. 错误处理
所有操作都包含了异常处理和日志记录，便于调试和监控。

## 扩展建议

1. **添加分页功能**: 在搜索方法中添加from和size参数
2. **添加排序功能**: 支持按价格、时间等字段排序
3. **添加聚合查询**: 统计分析功能
4. **添加高亮显示**: 搜索结果关键词高亮
5. **添加建议功能**: 搜索建议和自动补全

## 常见问题

### Q: 出现 "Java 8 date/time type LocalDateTime not supported" 错误？
A: 这是Jackson序列化Java 8时间类型的问题。解决方案：
1. 运行 `fix-jackson-issue.bat` 修复脚本
2. 项目已添加jackson-datatype-jsr310依赖
3. 配置了Jackson时间序列化格式
4. 重新编译并重启应用即可

### Q: 出现 "analyzer [ik_max_word] has not been configured" 错误？
A: 这是因为没有安装IK分词插件。解决方案：
1. 运行 `fix-elasticsearch.bat` 修复脚本
2. 或者手动删除并重新创建索引
3. 项目已修改为使用标准分析器，不再依赖IK插件

### Q: 连接Elasticsearch失败？
A: 检查Elasticsearch服务是否启动，端口是否正确，防火墙设置等。

### Q: 索引创建失败？
A:
1. 确保Elasticsearch服务正常运行
2. 检查索引是否已存在（可以先删除再创建）
3. 查看Elasticsearch日志获取详细错误信息

### Q: 中文搜索不准确？
A: 项目使用了自定义中文分析器，支持基本的中文搜索。如需更好的中文分词效果，建议安装IK分词插件。

### Q: 性能优化？
A: 可以调整批量操作大小、连接池配置、索引刷新策略等。
