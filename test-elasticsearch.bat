@echo off
chcp 65001 >nul
setlocal

REM Elasticsearch Demo 测试脚本
set BASE_URL=http://localhost:8088/elasticsearch

echo === Elasticsearch Demo 测试脚本 ===
echo 确保应用已启动在端口 8088
echo.

REM 1. 创建索引
echo 1. 创建索引...
curl -X POST "%BASE_URL%/index/create" -H "Content-Type: application/json"
echo.

REM 2. 初始化测试数据
echo 2. 初始化测试数据...
curl -X POST "%BASE_URL%/data/init" -H "Content-Type: application/json"
echo.

REM 3. 搜索所有商品
echo 3. 搜索所有商品...
curl -X GET "%BASE_URL%/product/search/all"
echo.

REM 4. 根据名称搜索
echo 4. 搜索iPhone相关商品...
curl -X GET "%BASE_URL%/product/search/name?name=iPhone"
echo.

REM 5. 根据分类搜索
echo 5. 搜索手机分类商品...
curl -X GET "%BASE_URL%/product/search/category?category=手机"
echo.

REM 6. 获取指定商品
echo 6. 获取ID为1的商品...
curl -X GET "%BASE_URL%/product/1"
echo.

REM 7. 添加新商品
echo 7. 添加新商品...
curl -X POST "%BASE_URL%/product/save" ^
  -H "Content-Type: application/json" ^
  -d "{\"id\": \"test-100\", \"name\": \"测试商品\", \"description\": \"这是一个通过API添加的测试商品\", \"price\": 999.99, \"category\": \"测试分类\", \"tags\": [\"测试\", \"API\", \"新增\"], \"stock\": 50, \"available\": true}"
echo.

REM 8. 更新商品
echo 8. 更新商品价格...
curl -X PUT "%BASE_URL%/product/test-100" ^
  -H "Content-Type: application/json" ^
  -d "{\"price\": 888.88, \"stock\": 30}"
echo.

REM 9. 再次获取更新后的商品
echo 9. 获取更新后的商品...
curl -X GET "%BASE_URL%/product/test-100"
echo.

REM 10. 复合搜索
echo 10. 复合搜索（华为+手机）...
curl -X GET "%BASE_URL%/product/search/complex?name=华为&category=手机"
echo.

echo === 测试完成 ===
pause
