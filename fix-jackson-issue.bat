@echo off
chcp 65001 >nul
setlocal

echo === Jackson时间序列化问题修复脚本 ===
echo.

echo 1. 重新编译项目...
call mvn clean compile -DskipTests
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)
echo 编译成功！
echo.

echo 2. 重新启动应用...
echo 请手动重启SpringBoot应用，然后按任意键继续...
pause
echo.

REM 设置API基础URL
set BASE_URL=http://localhost:8088/elasticsearch

echo 3. 等待应用启动...
timeout /t 5 /nobreak >nul

echo 4. 删除旧索引...
curl -X DELETE "%BASE_URL%/index/delete" -H "Content-Type: application/json"
echo.

echo 5. 创建新索引...
curl -X POST "%BASE_URL%/index/create" -H "Content-Type: application/json"
echo.

echo 6. 初始化测试数据...
curl -X POST "%BASE_URL%/data/init" -H "Content-Type: application/json"
echo.

echo 7. 测试搜索功能...
curl -X GET "%BASE_URL%/product/search/all"
echo.

echo 8. 测试保存新商品...
curl -X POST "%BASE_URL%/product/save" ^
  -H "Content-Type: application/json" ^
  -d "{\"id\": \"jackson-test\", \"name\": \"Jackson测试商品\", \"description\": \"测试时间序列化\", \"price\": 99.99, \"category\": \"测试\", \"tags\": [\"测试\", \"Jackson\"], \"stock\": 10, \"available\": true}"
echo.

echo 9. 获取刚保存的商品...
curl -X GET "%BASE_URL%/product/jackson-test"
echo.

echo === 修复完成 ===
echo 如果还有问题，请检查：
echo 1. Jackson依赖是否正确添加
echo 2. 应用是否正确重启
echo 3. Elasticsearch服务是否正常
pause
